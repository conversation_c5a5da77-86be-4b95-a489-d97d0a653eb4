#!/usr/bin/env python3
"""
Test script for social media scraper
"""
import pandas as pd
from social_media_scraper import SocialMediaScraper


def test_url_cleaning():
    """Test URL cleaning functionality"""
    scraper = SocialMediaScraper()
    
    # Test cases from your actual data
    test_urls = [
        'https://facebook.com/mofercoffee"">',
        '"https://www.facebook.com/compasscoffeedc"',
        'http://instagram.com/mofercoffee"">',
        'https://twitter.com/mofercoffee"">',
        '"https://www.instagram.com/forfive""',
        'https://www.facebook.com/SPoTCoffeeCafe""',
        'http://www.facebook.com/2008',
        'https://instagram.com/v',
        '',
        None
    ]
    
    print("🧪 TESTING URL CLEANING")
    print("=" * 50)
    
    for i, url in enumerate(test_urls, 1):
        print(f"\nTest {i}: {repr(url)}")
        cleaned = scraper.clean_social_media_url(url)
        platform = scraper.detect_platform(cleaned) if cleaned else None
        print(f"  Cleaned: {cleaned}")
        print(f"  Platform: {platform}")


def analyze_sample_data():
    """Analyze a sample of the social media data"""
    scraper = SocialMediaScraper()
    
    # Read just the first 10 rows for testing
    try:
        df = pd.read_csv("complete_independent_coffee_businesses_20250730_174823.csv", nrows=10)
        
        print("\n📊 SAMPLE DATA ANALYSIS")
        print("=" * 50)
        
        social_columns = [
            'facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url',
            'youtube_url', 'tiktok_url', 'pinterest_url', 'yelp_url'
        ]
        
        for index, row in df.iterrows():
            business_name = row.get('business_name', f'Business {index}')
            print(f"\n🏢 {business_name}")
            
            for col in social_columns:
                if col in row and pd.notna(row[col]) and str(row[col]).strip():
                    url = str(row[col]).strip()
                    platform = col.replace('_url', '')
                    
                    print(f"  {platform}: {repr(url)}")
                    
                    cleaned = scraper.clean_social_media_url(url)
                    if cleaned:
                        print(f"    ✅ Cleaned: {cleaned}")
                    else:
                        print(f"    ❌ Could not clean URL")
                        
    except FileNotFoundError:
        print("❌ CSV file not found. Please ensure 'complete_independent_coffee_businesses_20250730_174823.csv' is in the current directory.")
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")


def test_single_profile_scraping():
    """Test scraping a single social media profile"""
    scraper = SocialMediaScraper()
    
    # Test with a well-known coffee shop (if accessible)
    test_profiles = [
        "https://www.facebook.com/starbucks",  # Public page
        "https://www.instagram.com/starbucks",  # Public profile
    ]
    
    print("\n🔍 TESTING PROFILE SCRAPING")
    print("=" * 50)
    
    for url in test_profiles:
        print(f"\nTesting: {url}")
        result = scraper.scrape_social_media_profile(url)
        
        print(f"  Emails: {result['emails']}")
        print(f"  Phones: {result['phones']}")
        print(f"  Website: {result['website']}")
        print(f"  Description: {result['description'][:100]}..." if result['description'] else "  Description: None")
        print(f"  Error: {result['error']}")


if __name__ == "__main__":
    print("🚀 SOCIAL MEDIA SCRAPER TESTING")
    print("=" * 50)
    
    # Run tests
    test_url_cleaning()
    analyze_sample_data()
    
    # Ask if user wants to test actual scraping
    test_scraping = input("\n🤔 Do you want to test actual profile scraping? (y/n): ").lower().strip()
    if test_scraping == 'y':
        test_single_profile_scraping()
    
    print("\n✅ Testing completed!")
