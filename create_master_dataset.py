"""
Master Dataset Combiner
Combine all coffee business datasets into one master file with deduplication
"""
import pandas as pd
import numpy as np
from datetime import datetime
from difflib import SequenceMatcher
import math
import re


class MasterDatasetCombiner:
    """Combine and deduplicate all coffee business datasets"""
    
    def __init__(self):
        self.pittsburgh_lat = 40.4406
        self.pittsburgh_lng = -79.9959
        
    def calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """Calculate distance between two points in miles"""
        if pd.isna(lat1) or pd.isna(lng1) or pd.isna(lat2) or pd.isna(lng2):
            return float('inf')
            
        R = 3959  # Earth's radius in miles
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lng = math.radians(lng2 - lng1)
        
        a = (math.sin(delta_lat / 2) ** 2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lng / 2) ** 2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        
        return R * c
    
    def normalize_business_name(self, name: str) -> str:
        """Normalize business name for comparison"""
        if pd.isna(name) or not name:
            return ""
        
        # Convert to lowercase and remove common suffixes/prefixes
        name = str(name).lower().strip()
        
        # Remove common business suffixes
        suffixes = ['llc', 'inc', 'corp', 'ltd', 'co', 'company', 'restaurant', 'cafe', 'shop', 'coffee']
        words = name.split()
        filtered_words = [w for w in words if w not in suffixes]
        
        return ' '.join(filtered_words) if filtered_words else name
    
    def calculate_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity between two business names"""
        norm1 = self.normalize_business_name(name1)
        norm2 = self.normalize_business_name(name2)
        return SequenceMatcher(None, norm1, norm2).ratio()
    
    def are_duplicates(self, business1: pd.Series, business2: pd.Series) -> bool:
        """Check if two businesses are duplicates"""
        # Check name similarity
        name_similarity = self.calculate_similarity(business1['business_name'], business2['business_name'])
        
        # If names are very similar (>85% match)
        if name_similarity > 0.85:
            # Check if they're in the same location (within 0.2 miles)
            if (not pd.isna(business1.get('latitude')) and not pd.isna(business1.get('longitude')) and 
                not pd.isna(business2.get('latitude')) and not pd.isna(business2.get('longitude'))):
                
                distance = self.calculate_distance(
                    business1['latitude'], business1['longitude'],
                    business2['latitude'], business2['longitude']
                )
                
                if distance < 0.2:  # Within 0.2 miles
                    return True
        
        # Check if they have the same exact address
        addr1 = str(business1.get('full_address', '')).strip().lower()
        addr2 = str(business2.get('full_address', '')).strip().lower()
        if addr1 and addr2 and addr1 == addr2:
            return True
        
        return False
    
    def score_business_completeness(self, business: pd.Series) -> int:
        """Score business based on data completeness"""
        score = 0
        
        # Points for having data
        if not pd.isna(business.get('email_address')) and business.get('email_address'):
            score += 15
        if not pd.isna(business.get('phone_number')) and business.get('phone_number'):
            score += 12
        if not pd.isna(business.get('website_url')) and business.get('website_url'):
            score += 10
        if not pd.isna(business.get('full_address')) and business.get('full_address'):
            score += 8
        if not pd.isna(business.get('latitude')) and not pd.isna(business.get('longitude')):
            score += 8
        
        # Points for social media
        social_fields = ['facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url', 'youtube_url']
        for field in social_fields:
            if not pd.isna(business.get(field)) and business.get(field):
                score += 3
        
        # Points for additional data
        if not pd.isna(business.get('google_rating')) and business.get('google_rating'):
            score += 5
        if not pd.isna(business.get('business_hours')) and business.get('business_hours'):
            score += 4
        
        # Prefer Google Places data (higher quality)
        if business.get('data_source') == 'google_places':
            score += 10
        
        return score
    
    def merge_duplicate_businesses(self, duplicates: list) -> pd.Series:
        """Merge duplicate businesses, keeping the best data from each"""
        # Start with the business that has the highest completeness score
        best_business = max(duplicates, key=self.score_business_completeness)
        merged = best_business.copy()
        
        # Fill in missing data from other duplicates
        for other in duplicates:
            if other.name == best_business.name:
                continue
                
            # Fill missing fields with data from other businesses
            for field in other.index:
                if (pd.isna(merged.get(field)) or not merged.get(field)) and \
                   (not pd.isna(other.get(field)) and other.get(field)):
                    merged[field] = other[field]
        
        return merged
    
    def standardize_columns(self, df: pd.DataFrame, source_name: str) -> pd.DataFrame:
        """Standardize column names and add missing columns"""
        
        # Standard columns we want in the final dataset
        standard_columns = [
            'business_name', 'phone_number', 'email_address', 'website_url',
            'full_address', 'city', 'state', 'zip_code', 'latitude', 'longitude',
            'business_category', 'business_hours', 'distance_from_pittsburgh',
            'facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url',
            'youtube_url', 'tiktok_url', 'pinterest_url', 'yelp_url', 'google_business_url',
            'data_source', 'google_rating', 'google_review_count', 'price_level',
            'business_status', 'collected_at'
        ]
        
        # Add missing columns
        for col in standard_columns:
            if col not in df.columns:
                df[col] = ''
        
        # Calculate distance from Pittsburgh if not present
        if 'distance_from_pittsburgh' not in df.columns or df['distance_from_pittsburgh'].isna().all():
            df['distance_from_pittsburgh'] = df.apply(
                lambda row: self.calculate_distance(
                    self.pittsburgh_lat, self.pittsburgh_lng,
                    row.get('latitude', 0), row.get('longitude', 0)
                ) if not pd.isna(row.get('latitude')) and not pd.isna(row.get('longitude')) else 0,
                axis=1
            )
        
        # Add source identifier if not present
        if 'data_source' not in df.columns or df['data_source'].isna().all():
            df['data_source'] = source_name
        
        return df[standard_columns]
    
    def load_and_combine_datasets(self) -> pd.DataFrame:
        """Load all datasets and combine them"""
        
        datasets = []
        
        # Dataset 1: Original Google Places data with social media
        try:
            df1 = pd.read_csv('output/pittsburgh_independent_coffee_businesses_20250729_174100_with_emails_social_20250730_034301.csv')
            df1 = self.standardize_columns(df1, 'google_places_original')
            datasets.append(('Original Google Places Dataset', df1))
            print(f"✅ Loaded original dataset: {len(df1)} businesses")
        except FileNotFoundError:
            print("❌ Original dataset not found")
        
        # Dataset 2: New OpenStreetMap data
        try:
            df2 = pd.read_csv('complete_independent_coffee_businesses_20250730_174823.csv')
            df2 = self.standardize_columns(df2, 'openstreetmap_new')
            datasets.append(('New OpenStreetMap Dataset', df2))
            print(f"✅ Loaded new OpenStreetMap dataset: {len(df2)} businesses")
        except FileNotFoundError:
            print("❌ New OpenStreetMap dataset not found")
        
        # Dataset 3: Any other OpenStreetMap files
        import glob
        osm_files = glob.glob('openstreetmap_independent_coffee_*.csv')
        for file in osm_files:
            try:
                df_extra = pd.read_csv(file)
                df_extra = self.standardize_columns(df_extra, 'openstreetmap_extra')
                datasets.append((f'Extra OSM Dataset ({file})', df_extra))
                print(f"✅ Loaded extra dataset {file}: {len(df_extra)} businesses")
            except:
                print(f"❌ Could not load {file}")
        
        if not datasets:
            print("❌ No datasets found to combine!")
            return pd.DataFrame()
        
        # Combine all datasets
        print(f"\n🔄 Combining {len(datasets)} datasets...")
        combined_df = pd.concat([df for _, df in datasets], ignore_index=True, sort=False)
        
        print(f"📊 Total businesses before deduplication: {len(combined_df)}")
        return combined_df
    
    def remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Remove duplicate businesses"""
        print(f"\n🔍 Removing duplicates from {len(df)} businesses...")
        
        unique_businesses = []
        processed_indices = set()
        
        for i, (_, business) in enumerate(df.iterrows()):
            if i in processed_indices:
                continue
            
            if i % 500 == 0:
                print(f"  Processing {i}/{len(df)} businesses...")
            
            # Find all duplicates of this business
            duplicates = [business]
            duplicate_indices = {i}
            
            for j, (_, other_business) in enumerate(df.iloc[i+1:].iterrows(), i+1):
                if j in processed_indices:
                    continue
                
                if self.are_duplicates(business, other_business):
                    duplicates.append(other_business)
                    duplicate_indices.add(j)
            
            # Mark all duplicates as processed
            processed_indices.update(duplicate_indices)
            
            # Merge duplicates into best business
            merged_business = self.merge_duplicate_businesses(duplicates)
            unique_businesses.append(merged_business)
            
            if len(duplicates) > 1:
                print(f"  📍 Merged {len(duplicates)} duplicates for '{business['business_name']}'")
        
        result_df = pd.DataFrame(unique_businesses)
        print(f"✅ Removed {len(df) - len(result_df)} duplicates")
        print(f"📊 Final unique businesses: {len(result_df)}")
        
        return result_df
    
    def create_master_dataset(self) -> str:
        """Create the master dataset"""
        print("🎯 CREATING MASTER COFFEE BUSINESS DATASET")
        print("=" * 60)
        
        # Load and combine all datasets
        combined_df = self.load_and_combine_datasets()
        
        if combined_df.empty:
            return None
        
        # Remove duplicates
        master_df = self.remove_duplicates(combined_df)
        
        # Sort by distance from Pittsburgh
        master_df = master_df.sort_values('distance_from_pittsburgh')
        
        # Save master dataset
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"master_coffee_businesses_{timestamp}.csv"
        master_df.to_csv(filename, index=False)
        
        # Print comprehensive summary
        self.print_summary(master_df, filename)
        
        return filename
    
    def print_summary(self, df: pd.DataFrame, filename: str):
        """Print comprehensive summary of the master dataset"""
        
        print(f"\n🎉 MASTER DATASET CREATED: {filename}")
        print("=" * 60)
        
        # Basic stats
        total = len(df)
        with_emails = len(df[df['email_address'].notna() & (df['email_address'] != '')])
        with_phone = len(df[df['phone_number'].notna() & (df['phone_number'] != '')])
        with_website = len(df[df['website_url'].notna() & (df['website_url'] != '')])
        
        print(f"📊 TOTAL BUSINESSES: {total}")
        print(f"📧 WITH EMAILS: {with_emails} ({with_emails/total*100:.1f}%)")
        print(f"📞 WITH PHONE: {with_phone} ({with_phone/total*100:.1f}%)")
        print(f"🌐 WITH WEBSITES: {with_website} ({with_website/total*100:.1f}%)")
        
        # Social media stats
        social_fields = {
            'facebook_url': '📘 Facebook',
            'instagram_url': '📸 Instagram', 
            'twitter_url': '🐦 Twitter',
            'linkedin_url': '💼 LinkedIn',
            'youtube_url': '📺 YouTube'
        }
        
        print(f"\n📱 SOCIAL MEDIA COVERAGE:")
        for field, label in social_fields.items():
            count = len(df[df[field].notna() & (df[field] != '')])
            print(f"   {label}: {count}")
        
        # Data source breakdown
        print(f"\n📋 DATA SOURCES:")
        source_counts = df['data_source'].value_counts()
        for source, count in source_counts.items():
            print(f"   • {source}: {count} businesses")
        
        # Geographic distribution
        within_50 = len(df[df['distance_from_pittsburgh'] <= 50])
        within_100 = len(df[df['distance_from_pittsburgh'] <= 100])
        within_200 = len(df[df['distance_from_pittsburgh'] <= 200])
        
        print(f"\n🗺️  GEOGRAPHIC DISTRIBUTION:")
        print(f"   • Within 50 miles: {within_50}")
        print(f"   • Within 100 miles: {within_100}")
        print(f"   • Within 200 miles: {within_200}")
        print(f"   • Within 300 miles: {total}")
        
        print("=" * 60)
        print("✅ MASTER DATASET READY FOR MARKETING CAMPAIGNS!")


def main():
    """Create master dataset"""
    combiner = MasterDatasetCombiner()
    master_file = combiner.create_master_dataset()
    
    if master_file:
        print(f"\n🎯 SUCCESS! Master dataset created: {master_file}")
    else:
        print("\n❌ Failed to create master dataset")


if __name__ == "__main__":
    main()
