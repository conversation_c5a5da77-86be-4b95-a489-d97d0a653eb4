#!/usr/bin/env python3
"""
Enhanced Email Extractor - Better alternatives to social media scraping
Focuses on improving website-based email extraction
"""
import csv
import re
import time
import requests
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime
import random


class EnhancedEmailExtractor:
    """Enhanced email extraction using multiple strategies"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        self.timeout = 15
        
        # Enhanced email patterns
        self.email_patterns = [
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            r'mailto:([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'[A-Za-z0-9._%+-]+\s*@\s*[A-Za-z0-9.-]+\s*\.\s*[A-Z|a-z]{2,}',
            r'[A-Za-z0-9._%+-]+\s*\[\s*at\s*\]\s*[A-Za-z0-9.-]+\s*\[\s*dot\s*\]\s*[A-Z|a-z]{2,}',
        ]
        
        # Expanded contact page paths
        self.contact_paths = [
            '/contact', '/contact-us', '/contact.html', '/contactus', '/contact.php',
            '/about', '/about-us', '/about.html', '/aboutus', '/about.php',
            '/info', '/information', '/get-in-touch', '/reach-us', '/reach-out',
            '/support', '/help', '/customer-service', '/customer-support',
            '/team', '/staff', '/people', '/leadership', '/management',
            '/locations', '/location', '/store-info', '/hours', '/visit',
            '/feedback', '/inquiries', '/questions', '/faq', '/press',
            '/media', '/news', '/blog', '/careers', '/jobs', '/franchise',
            '/wholesale', '/business', '/corporate', '/catering'
        ]
        
        # Business email indicators
        self.business_email_indicators = [
            'info@', 'contact@', 'hello@', 'mail@', 'admin@',
            'support@', 'sales@', 'orders@', 'catering@',
            'wholesale@', 'business@', 'corporate@', 'press@'
        ]

    def extract_emails_from_text(self, text: str) -> list:
        """Extract emails with enhanced patterns"""
        emails = set()
        
        for pattern in self.email_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    email = match[0] if match[0] else match[1]
                else:
                    email = match
                
                email = email.lower().strip()
                
                # Clean up common obfuscations
                email = email.replace('[at]', '@').replace('[dot]', '.')
                email = email.replace(' at ', '@').replace(' dot ', '.')
                
                if self.is_valid_business_email(email):
                    emails.add(email)
        
        return list(emails)

    def is_valid_business_email(self, email: str) -> bool:
        """Check if email looks like a business email"""
        if not email or '@' not in email:
            return False
        
        # Skip personal email domains
        personal_domains = {
            'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
            'aol.com', 'icloud.com', 'live.com', 'msn.com'
        }
        
        domain = email.split('@')[1].lower()
        if domain in personal_domains:
            return False
        
        # Prefer business-looking emails
        for indicator in self.business_email_indicators:
            if email.startswith(indicator):
                return True
        
        # Check if domain matches business name patterns
        if any(word in domain for word in ['coffee', 'cafe', 'roast', 'bean', 'espresso']):
            return True
        
        return True

    def scrape_website_enhanced(self, url: str, business_name: str = '') -> dict:
        """Enhanced website scraping with multiple strategies"""
        result = {
            'emails': [],
            'phones': [],
            'additional_urls': [],
            'social_media': {},
            'error': None
        }
        
        if not url:
            result['error'] = 'No URL provided'
            return result
        
        try:
            # Clean URL
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            print(f"  🔍 Scraping: {url}")
            
            # Get main page
            response = self.session.get(url, timeout=self.timeout)
            if response.status_code != 200:
                result['error'] = f"HTTP {response.status_code}"
                return result
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract from main page
            main_text = soup.get_text()
            result['emails'].extend(self.extract_emails_from_text(main_text))
            
            # Look for contact pages
            base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"
            
            for path in self.contact_paths[:10]:  # Limit to avoid too many requests
                contact_url = base_url + path
                try:
                    time.sleep(random.uniform(1, 3))  # Rate limiting
                    contact_response = self.session.get(contact_url, timeout=self.timeout)
                    
                    if contact_response.status_code == 200:
                        contact_soup = BeautifulSoup(contact_response.content, 'html.parser')
                        contact_text = contact_soup.get_text()
                        contact_emails = self.extract_emails_from_text(contact_text)
                        
                        if contact_emails:
                            print(f"    ✅ Found emails on {path}: {contact_emails}")
                            result['emails'].extend(contact_emails)
                
                except:
                    continue
            
            # Remove duplicates and sort
            result['emails'] = sorted(list(set(result['emails'])))
            
            # Extract social media links
            social_patterns = {
                'facebook': r'https?://(?:www\.)?facebook\.com/[A-Za-z0-9._-]+',
                'instagram': r'https?://(?:www\.)?instagram\.com/[A-Za-z0-9._-]+',
                'twitter': r'https?://(?:www\.)?(?:twitter|x)\.com/[A-Za-z0-9._-]+',
                'linkedin': r'https?://(?:www\.)?linkedin\.com/(?:in|company)/[A-Za-z0-9._-]+'
            }
            
            page_html = str(soup)
            for platform, pattern in social_patterns.items():
                matches = re.findall(pattern, page_html, re.IGNORECASE)
                if matches:
                    result['social_media'][platform] = matches[0]
            
        except Exception as e:
            result['error'] = str(e)
        
        return result

    def create_social_media_contact_list(self, csv_file: str) -> str:
        """Create a list for manual social media verification"""
        print("📋 Creating social media contact verification list...")
        
        df = pd.read_csv(csv_file)
        
        # Filter businesses with social media but no email
        social_columns = ['facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url']
        
        verification_list = []
        
        for index, row in df.iterrows():
            has_social = any(pd.notna(row.get(col, '')) and str(row.get(col, '')).strip() 
                           for col in social_columns)
            has_email = pd.notna(row.get('email_address', '')) and str(row.get('email_address', '')).strip()
            
            if has_social and not has_email:
                business_data = {
                    'business_name': row.get('business_name', ''),
                    'phone_number': row.get('phone_number', ''),
                    'website_url': row.get('website_url', ''),
                    'city': row.get('city', ''),
                    'state': row.get('state', ''),
                }
                
                # Add social media URLs
                for col in social_columns:
                    if pd.notna(row.get(col, '')) and str(row.get(col, '')).strip():
                        platform = col.replace('_url', '')
                        business_data[f'{platform}_url'] = str(row.get(col, '')).strip()
                
                verification_list.append(business_data)
        
        # Save verification list
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"social_media_verification_list_{timestamp}.csv"
        
        if verification_list:
            verification_df = pd.DataFrame(verification_list)
            verification_df.to_csv(output_file, index=False)
            
            print(f"📁 Created verification list: {output_file}")
            print(f"📊 {len(verification_list)} businesses need manual verification")
        else:
            print("📊 No businesses found that need social media verification")
        
        return output_file

    def enhance_existing_emails(self, csv_file: str) -> str:
        """Re-scrape websites for businesses without emails"""
        print("🔍 Re-scraping websites for missing emails...")
        
        df = pd.read_csv(csv_file)
        
        # Find businesses with websites but no emails
        needs_scraping = df[
            (pd.notna(df['website_url'])) & 
            (df['website_url'].str.strip() != '') &
            ((pd.isna(df['email_address'])) | (df['email_address'].str.strip() == ''))
        ]
        
        print(f"📊 Found {len(needs_scraping)} businesses to re-scrape")
        
        enhanced_count = 0
        
        for index, row in needs_scraping.iterrows():
            business_name = row.get('business_name', f'Business {index}')
            website_url = str(row.get('website_url', '')).strip()
            
            print(f"\n[{enhanced_count + 1}/{len(needs_scraping)}] {business_name}")
            
            result = self.scrape_website_enhanced(website_url, business_name)
            
            if result['emails']:
                # Update the DataFrame
                df.at[index, 'email_address'] = '; '.join(result['emails'])
                enhanced_count += 1
                print(f"  ✅ Found {len(result['emails'])} emails: {result['emails']}")
            elif result['error']:
                print(f"  ❌ Error: {result['error']}")
            else:
                print(f"  ⚠️  No emails found")
        
        # Save enhanced dataset
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"enhanced_emails_{timestamp}.csv"
        df.to_csv(output_file, index=False)
        
        print(f"\n🎉 ENHANCEMENT COMPLETE!")
        print(f"📊 Enhanced {enhanced_count} businesses with new emails")
        print(f"📁 Saved to: {output_file}")
        
        return output_file


def main():
    """Main function"""
    extractor = EnhancedEmailExtractor()
    
    input_file = "complete_independent_coffee_businesses_20250730_174823.csv"
    
    if not pd.io.common.file_exists(input_file):
        print(f"❌ File not found: {input_file}")
        return
    
    print("🚀 ENHANCED EMAIL EXTRACTION")
    print("=" * 50)
    
    print("\n1. Creating social media verification list...")
    verification_file = extractor.create_social_media_contact_list(input_file)
    
    print("\n2. Re-scraping websites for missing emails...")
    enhanced_file = extractor.enhance_existing_emails(input_file)
    
    print(f"\n✅ Process complete!")
    print(f"📋 Manual verification list: {verification_file}")
    print(f"📊 Enhanced dataset: {enhanced_file}")


if __name__ == "__main__":
    main()
