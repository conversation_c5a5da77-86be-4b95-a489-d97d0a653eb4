#!/usr/bin/env python3
"""
Quick analysis of social media URLs in the dataset
"""
import pandas as pd
from social_media_scraper import SocialMediaScraper

def main():
    scraper = SocialMediaScraper()
    
    print("🔍 Analyzing social media URLs...")
    
    try:
        analysis = scraper.analyze_social_media_urls('complete_independent_coffee_businesses_20250730_174823.csv')
        
        print(f"\n🎯 READY TO SCRAPE!")
        print(f"Total URLs found: {sum(analysis['platform_counts'].values())}")
        print(f"Businesses with social media: {sum(1 for count in analysis['platform_counts'].values() if count > 0)}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
