#!/usr/bin/env python3
"""
Social Media Profile Scraper for Coffee Business Data
Extracts contact information from social media profiles
"""
import csv
import re
import time
import requests
from urllib.parse import urljoin, urlparse, unquote
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime
import os
import json
import random
from typing import Dict, List, Optional, Tuple


class SocialMediaScraper:
    """Scrapes contact information from social media profiles"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        self.timeout = 15
        self.delay_range = (2, 5)  # Random delay between requests
        
        # Email patterns for extraction
        self.email_patterns = [
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            r'mailto:([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})',
            r'[A-Za-z0-9._%+-]+\s*@\s*[A-Za-z0-9.-]+\s*\.\s*[A-Z|a-z]{2,}',
        ]
        
        # Phone patterns
        self.phone_patterns = [
            r'\+?1?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            r'\(\d{3}\)\s?\d{3}[-.]?\d{4}',
        ]
        
        # Platform-specific selectors
        self.platform_selectors = {
            'facebook': {
                'about_section': ['[data-overviewsection="contact_info"]', '.about_contact_info', '#about_contact_info'],
                'email_selectors': ['[href^="mailto:"]', 'a[href*="@"]'],
                'phone_selectors': ['[href^="tel:"]', '.phone', '[data-testid="phone"]'],
                'website_selectors': ['[data-testid="website"]', '.website', 'a[href*="http"]:not([href*="facebook"])'],
                'bio_selectors': ['.bio', '.description', '[data-testid="page_description"]']
            },
            'instagram': {
                'bio_selectors': ['.-vDIg', '.C4VMK', '._aa_c', '.rhpdm'],
                'email_selectors': ['a[href^="mailto:"]'],
                'website_selectors': ['a[href*="http"]:not([href*="instagram"])'],
                'contact_button': ['[data-testid="contact_button"]']
            },
            'twitter': {
                'bio_selectors': ['[data-testid="UserDescription"]', '.ProfileHeaderCard-bio'],
                'website_selectors': ['[data-testid="UserUrl"]', '.ProfileHeaderCard-url'],
                'location_selectors': ['[data-testid="UserLocation"]']
            },
            'linkedin': {
                'about_selectors': ['.org-about-us-organization-description', '.about-us'],
                'contact_selectors': ['.contact-info', '.org-contact-info'],
                'website_selectors': ['[data-test-id="about-us-website"]']
            }
        }

    def clean_social_media_url(self, url: str) -> Optional[str]:
        """Clean and validate social media URLs"""
        if not url or url.strip() == '':
            return None
            
        # Remove extra quotes and characters
        url = re.sub(r'^["\'>]+|["\'>]+$', '', url.strip())
        url = re.sub(r'["\'>]+>', '', url)
        url = url.split('"')[0]  # Take first part before quote
        
        # Handle malformed URLs
        if not url.startswith(('http://', 'https://')):
            if url.startswith('www.'):
                url = 'https://' + url
            elif any(platform in url for platform in ['facebook.com', 'instagram.com', 'twitter.com', 'linkedin.com']):
                url = 'https://' + url
            else:
                return None
        
        # Validate URL format
        try:
            parsed = urlparse(url)
            if not parsed.netloc:
                return None
            return url
        except:
            return None

    def detect_platform(self, url: str) -> Optional[str]:
        """Detect social media platform from URL"""
        if not url:
            return None
            
        url_lower = url.lower()
        if 'facebook.com' in url_lower or 'fb.com' in url_lower:
            return 'facebook'
        elif 'instagram.com' in url_lower:
            return 'instagram'
        elif 'twitter.com' in url_lower or 'x.com' in url_lower:
            return 'twitter'
        elif 'linkedin.com' in url_lower:
            return 'linkedin'
        elif 'youtube.com' in url_lower or 'youtu.be' in url_lower:
            return 'youtube'
        elif 'tiktok.com' in url_lower:
            return 'tiktok'
        elif 'pinterest.com' in url_lower:
            return 'pinterest'
        elif 'yelp.com' in url_lower:
            return 'yelp'
        
        return None

    def extract_emails_from_text(self, text: str) -> List[str]:
        """Extract email addresses from text"""
        emails = set()
        for pattern in self.email_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0] if match[0] else match[1]
                email = match.lower().strip()
                if '@' in email and '.' in email.split('@')[1]:
                    emails.add(email)
        return list(emails)

    def extract_phones_from_text(self, text: str) -> List[str]:
        """Extract phone numbers from text"""
        phones = set()
        for pattern in self.phone_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    phone = ''.join(match)
                else:
                    phone = match
                # Clean phone number
                phone = re.sub(r'[^\d]', '', phone)
                if len(phone) >= 10:
                    phones.add(phone)
        return list(phones)

    def scrape_facebook_profile(self, url: str) -> Dict:
        """Scrape Facebook business page"""
        result = {
            'emails': [],
            'phones': [],
            'website': '',
            'description': '',
            'address': '',
            'error': None
        }
        
        try:
            # Add mobile user agent for better access
            headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
            }
            
            response = self.session.get(url, headers=headers, timeout=self.timeout)
            if response.status_code != 200:
                result['error'] = f"HTTP {response.status_code}"
                return result
                
            soup = BeautifulSoup(response.content, 'html.parser')
            page_text = soup.get_text()
            
            # Extract emails and phones from page text
            result['emails'] = self.extract_emails_from_text(page_text)
            result['phones'] = self.extract_phones_from_text(page_text)
            
            # Try to find specific elements
            selectors = self.platform_selectors['facebook']
            
            # Look for website links
            for selector in selectors['website_selectors']:
                elements = soup.select(selector)
                for elem in elements:
                    href = elem.get('href', '')
                    if href and 'http' in href and 'facebook.com' not in href:
                        result['website'] = href
                        break
                if result['website']:
                    break
            
            # Look for description/bio
            for selector in selectors['bio_selectors']:
                elements = soup.select(selector)
                for elem in elements:
                    text = elem.get_text(strip=True)
                    if text and len(text) > 20:
                        result['description'] = text[:500]  # Limit length
                        break
                if result['description']:
                    break
                    
        except Exception as e:
            result['error'] = str(e)
            
        return result

    def scrape_instagram_profile(self, url: str) -> Dict:
        """Scrape Instagram business profile"""
        result = {
            'emails': [],
            'phones': [],
            'website': '',
            'description': '',
            'error': None
        }
        
        try:
            response = self.session.get(url, timeout=self.timeout)
            if response.status_code != 200:
                result['error'] = f"HTTP {response.status_code}"
                return result
                
            soup = BeautifulSoup(response.content, 'html.parser')
            page_text = soup.get_text()
            
            # Extract emails and phones from page text
            result['emails'] = self.extract_emails_from_text(page_text)
            result['phones'] = self.extract_phones_from_text(page_text)
            
            # Look for JSON data in script tags
            script_tags = soup.find_all('script', type='application/ld+json')
            for script in script_tags:
                try:
                    data = json.loads(script.string)
                    if isinstance(data, dict):
                        # Extract contact info from structured data
                        if 'contactPoint' in data:
                            contact = data['contactPoint']
                            if 'email' in contact:
                                result['emails'].append(contact['email'])
                            if 'telephone' in contact:
                                result['phones'].append(contact['telephone'])
                except:
                    continue
                    
        except Exception as e:
            result['error'] = str(e)
            
        return result

    def scrape_generic_profile(self, url: str, platform: str) -> Dict:
        """Generic scraper for other platforms"""
        result = {
            'emails': [],
            'phones': [],
            'website': '',
            'description': '',
            'error': None
        }
        
        try:
            response = self.session.get(url, timeout=self.timeout)
            if response.status_code != 200:
                result['error'] = f"HTTP {response.status_code}"
                return result
                
            soup = BeautifulSoup(response.content, 'html.parser')
            page_text = soup.get_text()
            
            # Extract emails and phones from page text
            result['emails'] = self.extract_emails_from_text(page_text)
            result['phones'] = self.extract_phones_from_text(page_text)
            
            # Platform-specific extraction
            if platform in self.platform_selectors:
                selectors = self.platform_selectors[platform]
                
                # Extract website
                if 'website_selectors' in selectors:
                    for selector in selectors['website_selectors']:
                        elements = soup.select(selector)
                        for elem in elements:
                            href = elem.get('href', '')
                            if href and 'http' in href and platform not in href:
                                result['website'] = href
                                break
                        if result['website']:
                            break
                            
        except Exception as e:
            result['error'] = str(e)
            
        return result

    def scrape_social_media_profile(self, url: str) -> Dict:
        """Main method to scrape any social media profile"""
        cleaned_url = self.clean_social_media_url(url)
        if not cleaned_url:
            return {'error': 'Invalid URL', 'emails': [], 'phones': [], 'website': '', 'description': ''}
            
        platform = self.detect_platform(cleaned_url)
        if not platform:
            return {'error': 'Unknown platform', 'emails': [], 'phones': [], 'website': '', 'description': ''}
        
        print(f"  🔍 Scraping {platform}: {cleaned_url}")
        
        # Add random delay to avoid rate limiting
        time.sleep(random.uniform(*self.delay_range))
        
        # Platform-specific scraping
        if platform == 'facebook':
            return self.scrape_facebook_profile(cleaned_url)
        elif platform == 'instagram':
            return self.scrape_instagram_profile(cleaned_url)
        else:
            return self.scrape_generic_profile(cleaned_url, platform)

    def process_csv_file(self, input_file: str, output_file: str = None) -> str:
        """Process CSV file and scrape social media profiles"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"enhanced_social_media_{timestamp}.csv"

        print(f"📊 Processing {input_file}...")

        # Read CSV file
        df = pd.read_csv(input_file)

        # Social media columns to process
        social_columns = [
            'facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url',
            'youtube_url', 'tiktok_url', 'pinterest_url', 'yelp_url'
        ]

        # Add new columns for extracted data
        new_columns = [
            'social_emails_found', 'social_phones_found', 'social_websites_found',
            'social_descriptions_found', 'social_scraping_errors', 'social_platforms_scraped'
        ]

        for col in new_columns:
            if col not in df.columns:
                df[col] = ''

        total_businesses = len(df)
        processed = 0

        print(f"🔍 Found {total_businesses} businesses to process")

        for index, row in df.iterrows():
            processed += 1
            business_name = row.get('business_name', f'Business {index}')

            print(f"\n[{processed}/{total_businesses}] Processing: {business_name}")

            all_emails = set()
            all_phones = set()
            all_websites = set()
            all_descriptions = []
            all_errors = []
            platforms_scraped = []

            # Process each social media platform
            for col in social_columns:
                if col in row and pd.notna(row[col]) and row[col].strip():
                    url = str(row[col]).strip()
                    platform = col.replace('_url', '')

                    print(f"  📱 Processing {platform}...")

                    result = self.scrape_social_media_profile(url)

                    if result['error']:
                        all_errors.append(f"{platform}: {result['error']}")
                        print(f"    ❌ Error: {result['error']}")
                    else:
                        platforms_scraped.append(platform)

                        # Collect emails
                        if result['emails']:
                            all_emails.update(result['emails'])
                            print(f"    ✅ Found {len(result['emails'])} emails")

                        # Collect phones
                        if result['phones']:
                            all_phones.update(result['phones'])
                            print(f"    ✅ Found {len(result['phones'])} phones")

                        # Collect websites
                        if result['website']:
                            all_websites.add(result['website'])
                            print(f"    ✅ Found website: {result['website']}")

                        # Collect descriptions
                        if result['description']:
                            all_descriptions.append(f"{platform}: {result['description']}")
                            print(f"    ✅ Found description")

            # Update DataFrame with results
            df.at[index, 'social_emails_found'] = '; '.join(sorted(all_emails))
            df.at[index, 'social_phones_found'] = '; '.join(sorted(all_phones))
            df.at[index, 'social_websites_found'] = '; '.join(sorted(all_websites))
            df.at[index, 'social_descriptions_found'] = ' | '.join(all_descriptions)
            df.at[index, 'social_scraping_errors'] = '; '.join(all_errors)
            df.at[index, 'social_platforms_scraped'] = '; '.join(platforms_scraped)

            # Print summary for this business
            print(f"  📊 Summary: {len(all_emails)} emails, {len(all_phones)} phones, {len(all_websites)} websites")

            # Save progress every 10 businesses
            if processed % 10 == 0:
                df.to_csv(output_file, index=False)
                print(f"💾 Progress saved to {output_file}")

        # Final save
        df.to_csv(output_file, index=False)

        # Print final statistics
        total_emails = sum(1 for _, row in df.iterrows() if row['social_emails_found'])
        total_phones = sum(1 for _, row in df.iterrows() if row['social_phones_found'])
        total_websites = sum(1 for _, row in df.iterrows() if row['social_websites_found'])

        print(f"\n🎉 SCRAPING COMPLETE!")
        print(f"📊 Final Statistics:")
        print(f"   • Businesses processed: {total_businesses}")
        print(f"   • Businesses with emails found: {total_emails}")
        print(f"   • Businesses with phones found: {total_phones}")
        print(f"   • Businesses with websites found: {total_websites}")
        print(f"📁 Enhanced data saved to: {output_file}")

        return output_file

    def analyze_social_media_urls(self, csv_file: str) -> Dict:
        """Analyze the quality and distribution of social media URLs"""
        print(f"🔍 Analyzing social media URLs in {csv_file}...")

        df = pd.read_csv(csv_file)

        social_columns = [
            'facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url',
            'youtube_url', 'tiktok_url', 'pinterest_url', 'yelp_url'
        ]

        analysis = {
            'total_businesses': len(df),
            'platform_counts': {},
            'url_quality': {},
            'sample_urls': {}
        }

        for col in social_columns:
            if col in df.columns:
                platform = col.replace('_url', '')

                # Count non-empty URLs
                non_empty = df[col].notna() & (df[col] != '') & (df[col].str.strip() != '')
                count = non_empty.sum()

                analysis['platform_counts'][platform] = count

                if count > 0:
                    # Sample URLs for quality check
                    sample_urls = df[df[col].notna() & (df[col] != '')][col].head(3).tolist()
                    analysis['sample_urls'][platform] = sample_urls

                    # Check URL quality
                    valid_urls = 0
                    for url in df[df[col].notna()][col]:
                        if self.clean_social_media_url(str(url)):
                            valid_urls += 1

                    analysis['url_quality'][platform] = {
                        'total': count,
                        'valid': valid_urls,
                        'quality_percentage': (valid_urls / count * 100) if count > 0 else 0
                    }

        # Print analysis
        print(f"\n📊 SOCIAL MEDIA URL ANALYSIS")
        print(f"Total businesses: {analysis['total_businesses']}")
        print(f"\n📱 Platform Distribution:")

        for platform, count in analysis['platform_counts'].items():
            percentage = (count / analysis['total_businesses'] * 100) if analysis['total_businesses'] > 0 else 0
            quality = analysis['url_quality'].get(platform, {}).get('quality_percentage', 0)
            print(f"   • {platform.capitalize()}: {count} URLs ({percentage:.1f}%) - {quality:.1f}% valid")

        return analysis


def main():
    """Main function to run social media scraping"""
    scraper = SocialMediaScraper()

    # Default input file
    input_file = "complete_independent_coffee_businesses_20250730_174823.csv"

    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        print("Please ensure the CSV file is in the current directory.")
        return

    print("🚀 SOCIAL MEDIA SCRAPER STARTING")
    print("=" * 50)

    # First, analyze the data
    analysis = scraper.analyze_social_media_urls(input_file)

    # Ask user if they want to proceed
    total_urls = sum(analysis['platform_counts'].values())
    print(f"\n📊 Found {total_urls} social media URLs to scrape")

    proceed = input("\n🤔 Do you want to proceed with scraping? (y/n): ").lower().strip()

    if proceed == 'y':
        print("\n🔍 Starting social media scraping...")
        output_file = scraper.process_csv_file(input_file)
        print(f"\n✅ Scraping completed! Results saved to: {output_file}")
    else:
        print("👋 Scraping cancelled.")


if __name__ == "__main__":
    main()
